
  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/

 :: Spring Boot ::                (v3.4.5)

2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.Application                      ][m   Starting Application v9.4.0-SNAPSHOT using Java 21.0.7 with PID 51260 (/Users/<USER>/Documents/TMS/openrouteservice/ors-api/target/ors.jar started by narayan.bansal in /Users/<USER>/Documents/TMS/openrouteservice)
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.Application                      ][m   The following 1 profile is active: "default"
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   Configuration lookup started.
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   Configuration file lookup by default locations.
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   Loaded file './ors-config.yml'.
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   Environment variables overriding openrouteservice configuration parameters detected: 
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   ors.config.location=./ors-config.yml
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   Configuration lookup finished.
2025-07-13 23:15:55 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.ORSEnvironmentPostProcessor      ][m   
2025-07-13 23:15:56 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.a.s.l.ORSInitContextListener       ][m   Initializing ORS...
2025-07-13 23:15:56 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   Total - 8 GB, Free - 7.96 GB, Max: 12 GB, Used - 40.05 MB
2025-07-13 23:15:56 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ====> Initializing 2 profiles (1 threads) ...
2025-07-13 23:15:56 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   2 profile configurations submitted as tasks.
2025-07-13 23:15:56 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CorePreparationHandler     ][m   Using 1 threads for ch preparation threads
2025-07-13 23:15:56 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Using 1 threads for lm preparation threads
2025-07-13 23:15:56 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CorePreparationHandler     ][m   Using 1 threads for ch preparation threads
2025-07-13 23:15:56 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Using 1 threads for lm preparation threads
2025-07-13 23:15:56 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Loaded landmark splitting collection from 
2025-07-13 23:15:56 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.Application                      ][m   Started Application in 1.448 seconds (process running for 7.03)
2025-07-13 23:15:56 [32mINFO   [m [36m                                              main[m [1;36m[ o.h.o.a.Application                      ][m   openrouteservice {"build_date":"2025-07-13T14:44:15Z","graph_version":"2","version":"9.4.0"}
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CorePreparationHandler     ][m   Creating CH preparations, totalMB:11008, usedMB:4962
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Creating LM preparations, totalMB:11008, usedMB:4962
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Finished LM preparation, totalMB:11008, usedMB:5963
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.ORSGraphHopper               ][m   version v4.9.8|2025-04-28T13:05:33Z (7,20,5,4,5,7)
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.g.e.ORSGraphHopper               ][m   graph CH|car_ors|RAM_STORE|3D|turn_cost|7,20,5,4,5, details:edges:22 187 030(762MB), nodes:17 708 874(338MB), name:(7MB), geo:186 525 040(712MB), bounds:68.262955,97.3302567,6.7568875,35.5958641,-59.0,5917.0, shortcuts:14 480 036 (277MB), nodesCH:17 708 874 (136MB), shortcuts:20 570 825 (628MB), nodesCH:17 708 874 (136MB), shortcuts:23 813 230 (727MB), nodesCH:17 708 874 (136MB)
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [1] Profile: 'driving-car', encoder: 'driving-car', location: 'graphs/driving-car'.
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [1] Edges: ******** - Nodes: ********.
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [1] Total time: 2.48s.
2025-07-13 23:15:58 [32mINFO   [m [36m                                ORS-pl-driving-car[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [1] Finished at: 2025-07-13 23:15:58.
2025-07-13 23:15:58 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.c.CorePreparationHandler     ][m   Using 1 threads for ch preparation threads
2025-07-13 23:15:58 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Using 1 threads for lm preparation threads
2025-07-13 23:15:58 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.c.CorePreparationHandler     ][m   Using 1 threads for ch preparation threads
2025-07-13 23:15:58 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.c.CoreLMPreparationHandler   ][m   Using 1 threads for lm preparation threads
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.ORSGraphHopper               ][m   version v4.9.8|2025-04-28T13:05:33Z (7,20,5,4,5,7)
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.g.e.ORSGraphHopper               ][m   graph bike_ors|RAM_STORE|3D|turn_cost|7,20,5,4,5, details:edges:22 542 174(774MB), nodes:17 986 139(344MB), name:(7MB), geo:190 261 151(726MB), bounds:68.262955,97.3302567,6.7568875,35.5958641,-59.0,6359.0
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [2] Profile: 'cycling-regular', encoder: 'cycling-regular', location: 'graphs/cycling-regular'.
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [2] Edges: ******** - Nodes: ********.
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [2] Total time: 2.311s.
2025-07-13 23:16:01 [32mINFO   [m [36m                            ORS-pl-cycling-regular[m [1;36m[ o.h.o.r.RoutingProfile                   ][m   [2] Finished at: 2025-07-13 23:16:01.
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   Total time: 4.872s.
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ========================================================================
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ====> Recycling garbage...
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   Before: Total - 12 GB, Free - 1.34 GB, Max: 12 GB, Used - 10.66 GB
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   After: Total - 12 GB, Free - 2.67 GB, Max: 12 GB, Used - 9.33 GB
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ========================================================================
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ====> Memory usage by profiles:
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   [1] 4.68 GB (50.1%)
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   [2] 4.10 GB (43.9%)
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   Total: 8.78 GB (94.0%)
2025-07-13 23:16:01 [32mINFO   [m [36m                                          ORS-Init[m [1;36m[ o.h.o.r.RoutingProfileManager            ][m   ========================================================================
2025-07-13 23:32:40 [32mINFO   [m [36m                     SpringApplicationShutdownHook[m [1;36m[ o.h.o.a.s.l.ORSInitContextListener       ][m   Shutting down openrouteservice {"build_date":"2025-07-13T14:44:15Z","graph_version":"2","graph_date":"2025-07-13T16:01:06Z","osm_date":"2025-07-11T20:20:56Z","version":"9.4.0"} and releasing resources.
