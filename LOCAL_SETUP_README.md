# OpenRouteService (ORS) Complete Local Docker Setup Guide

This comprehensive guide provides everything you need to run OpenRouteService locally using Docker with India map data from OpenStreetMap. Follow this guide from start to finish to have a fully functional routing service on your PC.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Initial Setup](#initial-setup)
3. [Configuration Explained](#configuration-explained)
4. [Starting the Service](#starting-the-service)
5. [Monitoring Graph Building](#monitoring-graph-building)
6. [Testing the Service](#testing-the-service)
7. [Service Management](#service-management)
8. [Configuration Changes](#configuration-changes)
9. [Troubleshooting](#troubleshooting)
10. [API Usage Examples](#api-usage-examples)
11. [Performance Optimization](#performance-optimization)

## 🔧 Prerequisites

### System Requirements

**Minimum Requirements:**
- **RAM**: 12GB available (16GB total recommended)
- **Storage**: 50GB free disk space
- **CPU**: 4 cores
- **OS**: Windows 10/11, macOS, or Linux

**Recommended for Production:**
- **RAM**: 32GB+
- **Storage**: 100GB+ SSD
- **CPU**: 8+ cores

### Software Requirements

1. **Docker Desktop** (Windows/Mac) or **Docker Engine** (Linux)
   ```bash
   # Verify Docker installation
   docker --version
   docker-compose --version
   ```

2. **Git** (for cloning the repository)
   ```bash
   git --version
   ```

3. **curl** (for testing APIs)
   ```bash
   curl --version
   ```

## 🚀 Initial Setup

### Step 1: Clone the Repository

```bash
# Clone the repository
git clone <your-repository-url>
cd openrouteservice

# Verify the project structure
ls -la
```

### Step 2: Verify Required Files

Ensure these files exist in your project:
```
openrouteservice/
├── docker-compose.yml          # Docker Compose configuration
├── custom-ors-config.yml       # Custom ORS configuration
├── ors-config.yml              # Main ORS configuration
├── ors-docker/
│   └── files/
│       └── india-latest.osm.pbf # India OSM data (~2GB)
└── LOCAL_SETUP_README.md       # This guide
```

### Step 3: Download India OSM Data (if not present)

If the India OSM file is missing:
```bash
# Create directories
mkdir -p ors-docker/files

# Download India OSM data (this will take time - ~2GB file)
cd ors-docker/files
wget https://download.geofabrik.de/asia/india-latest.osm.pbf
cd ../..
```

## ⚙️ Configuration Explained

### Docker Compose Configuration (`docker-compose.yml`)

```yaml
services:
  ors-app:
    image: heigit/openrouteservice:latest
    container_name: ors-app
    ports:
      - "8080:8082"                    # Maps host port 8080 to container port 8082
    volumes:
      - ./ors-docker:/home/<USER>
      - ./custom-ors-config.yml:/home/<USER>/config/ors-config.yml  # Mount config
    environment:
      BUILD_GRAPHS: "True"             # Build graphs on startup
      REBUILD_GRAPHS: "True"           # Force rebuild if graphs exist
      JAVA_HEAP_MIN: "16g"            # Minimum Java heap size
      JAVA_HEAP_MAX: "24g"            # Maximum Java heap size
      OSM_FILE: "/home/<USER>/files/india-latest.osm.pbf"  # OSM data file
      DATAREADER: "OSM"               # Data reader type
      PROFILE_LANDMARKS: "false"       # Disable landmarks for memory saving
      ORS_CONFIG: "/home/<USER>/config/ors-config.yml"     # Config file path
    deploy:
      resources:
        limits:
          memory: 32g                  # Container memory limit
        reservations:
          memory: 16g                  # Reserved memory
```

### ORS Configuration (`custom-ors-config.yml`)

```yaml
ors:
  engine:
    source_file: "/home/<USER>/files/india-latest.osm.pbf"  # OSM data source
    graphs_root_path: "/home/<USER>/graphs"                 # Graph storage path
    profile_default:
      build:
        source_file: "/home/<USER>/files/india-latest.osm.pbf"
        elevation: false                                 # Disable elevation for 2D routing
    profiles:
      driving-car:
        enabled: true                                    # Enable car routing
        build:
          elevation: false                               # Disable elevation
        service:
          maximum_distance: 5000000                      # 5000km max distance
        preparation:
          min_network_size: 200                          # Minimum network size
          min_one_way_network_size: 200                  # Minimum one-way network size
      walking:
        enabled: false                                   # Disabled to save memory
      cycling-regular:
        enabled: false                                   # Disabled to save memory
```

**Key Configuration Points:**
- **Maximum Distance**: Set to 5,000,000 meters (5000km)
- **Elevation**: Disabled for better performance and memory usage
- **Profiles**: Only driving-car enabled initially (others can be added later)
- **Memory**: Optimized for systems with 16-32GB RAM

## 🏁 Starting the Service

### Step 1: Start the Service

```bash
# Start ORS in background
docker-compose up -d

# Expected output:
# [+] Running 2/2
#  ✔ Network openrouteservice_default  Created
#  ✔ Container ors-app                 Started
```

### Step 2: Verify Container is Running

```bash
# Check container status
docker-compose ps

# Expected output:
# NAME      IMAGE                            COMMAND            SERVICE   CREATED         STATUS                   PORTS
# ors-app   heigit/openrouteservice:latest   "/entrypoint.sh"   ors-app   X minutes ago   Up X minutes (healthy)   0.0.0.0:8080->8082/tcp
```

## 📊 Monitoring Graph Building

### Real-time Log Monitoring

The first startup will take 30-60 minutes to build routing graphs from India OSM data. Monitor progress:

```bash
# Follow logs in real-time
docker-compose logs -f ors-app

# To stop monitoring, press Ctrl+C
```

### Understanding Log Messages

**Startup Phase:**
```
INFO  [ o.h.o.a.Application ] Starting Application v9.1.2
INFO  [ o.h.o.r.RoutingProfileManager ] ====> Initializing 1 profiles (1 threads) ...
```

**Graph Building Phase:**
```
INFO  [ o.h.o.r.g.e.ORSGraphHopper ] start creating graph from /home/<USER>/files/india-latest.osm.pbf
INFO  [ o.h.o.r.g.e.ORSGraphHopper ] using car_ors|RAM_STORE|2D|turn_cost
```

**Progress Indicators:**
```
INFO  [ o.h.o.r.g.e.ORSGraphHopper ] Finished reading OSM file: nodes: 45,123,456, edges: 12,345,678
INFO  [ o.h.o.r.g.e.c.CorePreparationHandler ] Creating CH preparations
```

**Completion:**
```
INFO  [ o.h.o.r.RoutingProfile ] [1] Profile: 'driving-car', encoder: 'driving-car'
INFO  [ o.h.o.r.RoutingProfile ] [1] Edges: 12,345,678 - Nodes: 45,123,456
INFO  [ o.h.o.r.RoutingProfile ] [1] Total time: 45.123s
INFO  [ o.h.o.a.Application ] openrouteservice {"build_date":"...","version":"9.1.2"}
```

### Monitoring Commands

```bash
# Check logs without following
docker-compose logs ors-app

# Check last 50 lines
docker-compose logs --tail=50 ors-app

# Check container resource usage
docker stats ors-app

# Check if service is responding
curl -X GET "http://localhost:8080/ors/v2/health"
```

## 🧪 Testing the Service

### Health Check

```bash
# Basic health check
curl -X GET "http://localhost:8080/ors/v2/health"

# Expected response:
# {"status":"ready"}
```

### Basic Routing Test

```bash
# Test with sample coordinates (Heidelberg area)
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{"coordinates": [[8.681495, 49.41461], [8.687872, 49.420318]]}'
```

### Long Distance Test (5000km limit verification)

```bash
# Test with India coordinates (once India data is loaded)
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      [77.5946, 12.9716],
      [78.4867, 17.3850]
    ]
  }'
```

## 🔧 Service Management

### Starting and Stopping

```bash
# Start service
docker-compose up -d

# Stop service (keeps data)
docker-compose down

# Stop and remove all data (full reset)
docker-compose down -v

# Restart service
docker-compose restart ors-app

# View service status
docker-compose ps
```

### Log Management

```bash
# View all logs
docker-compose logs ors-app

# Follow logs in real-time
docker-compose logs -f ors-app

# View last N lines
docker-compose logs --tail=100 ors-app

# View logs with timestamps
docker-compose logs -t ors-app
```

### Resource Monitoring

```bash
# Monitor container resources
docker stats ors-app

# Check disk usage
docker system df

# Check container details
docker inspect ors-app
```

## 🔄 Configuration Changes

### Changing Maximum Distance

1. **Edit Configuration File:**
   ```bash
   # Edit custom-ors-config.yml
   nano custom-ors-config.yml

   # Change maximum_distance value:
   service:
     maximum_distance: 3000000  # 3000km instead of 5000km
   ```

2. **Restart Service:**
   ```bash
   docker-compose restart ors-app
   ```

### Enabling Additional Profiles

1. **Edit Configuration:**
   ```yaml
   # In custom-ors-config.yml, enable walking profile:
   walking:
     enabled: true
     build:
       elevation: false
     service:
       maximum_distance: 5000000
     preparation:
       min_network_size: 200
       min_one_way_network_size: 200
   ```

2. **Force Rebuild:**
   ```bash
   # Stop and remove volumes to force rebuild
   docker-compose down -v
   docker-compose up -d
   ```

### Changing Memory Settings

1. **Edit Docker Compose:**
   ```yaml
   # In docker-compose.yml:
   environment:
     JAVA_HEAP_MIN: "8g"    # Reduce for systems with less RAM
     JAVA_HEAP_MAX: "16g"   # Reduce for systems with less RAM
   deploy:
     resources:
       limits:
         memory: 20g        # Reduce container limit
   ```

2. **Restart:**
   ```bash
   docker-compose down
   docker-compose up -d
   ```

### Switching OSM Data

1. **Download New Data:**
   ```bash
   cd ors-docker/files
   # Download different region, e.g., Germany
   wget https://download.geofabrik.de/europe/germany-latest.osm.pbf
   ```

2. **Update Configuration:**
   ```yaml
   # In docker-compose.yml:
   OSM_FILE: "/home/<USER>/files/germany-latest.osm.pbf"

   # In custom-ors-config.yml:
   source_file: "/home/<USER>/files/germany-latest.osm.pbf"
   ```

3. **Force Rebuild:**
   ```bash
   docker-compose down -v
   docker-compose up -d
   ```

## 🛠️ Troubleshooting

### Common Issues and Solutions

#### 1. OutOfMemoryError

**Symptoms:**
```
ERROR: java.lang.OutOfMemoryError: Java heap space
```

**Solutions:**
```bash
# Option A: Increase heap size
# Edit docker-compose.yml:
JAVA_HEAP_MIN: "20g"
JAVA_HEAP_MAX: "28g"

# Option B: Increase container memory
deploy:
  resources:
    limits:
      memory: 36g

# Option C: Use smaller OSM data
# Download a smaller region instead of full India
```

#### 2. Service Not Starting

**Symptoms:**
```
Container exits with code 1
Service shows as "Exited"
```

**Diagnosis:**
```bash
# Check logs for errors
docker-compose logs ors-app

# Check container status
docker-compose ps
```

**Solutions:**
```bash
# Reset everything
docker-compose down -v
docker-compose up -d

# Check system resources
free -h
df -h
```

#### 3. Port Already in Use

**Symptoms:**
```
Error: bind: address already in use
```

**Solutions:**
```bash
# Find what's using port 8080
lsof -i :8080
# or
netstat -tulpn | grep 8080

# Kill the process
kill <PID>

# Or change port in docker-compose.yml
ports:
  - "8081:8082"  # Use port 8081 instead
```

#### 4. Graph Building Fails

**Symptoms:**
```
ERROR: Couldn't load from existing folder
ERROR: Cannot use file for DataReader
```

**Solutions:**
```bash
# Verify OSM file exists
ls -la ors-docker/files/

# Check file permissions
chmod 644 ors-docker/files/india-latest.osm.pbf

# Force rebuild
docker-compose down -v
docker-compose up -d
```

#### 5. Configuration Not Applied

**Symptoms:**
- Distance limit still 100km
- Profiles not enabled/disabled as expected

**Solutions:**
```bash
# Verify configuration file is mounted
docker exec ors-app cat /home/<USER>/config/ors-config.yml

# Restart with force rebuild
docker-compose down -v
docker-compose up -d
```

### Performance Issues

#### Slow Graph Building

**Optimization:**
```bash
# Use SSD storage
# Increase CPU cores allocation in Docker Desktop
# Close other applications to free RAM
```

#### High Memory Usage

**Monitoring:**
```bash
# Monitor memory usage
docker stats ors-app

# Check system memory
free -h
```

**Optimization:**
```bash
# Disable unused profiles
# Reduce heap size if system has limited RAM
# Use smaller OSM extracts for testing
```

## 🌐 API Usage Examples

### Health and Status Endpoints

```bash
# Health check
curl -X GET "http://localhost:8080/ors/v2/health"
# Response: {"status":"ready"}

# Detailed status (if available)
curl -X GET "http://localhost:8080/ors/v2/status"
```

### Routing Examples

#### Short Distance (Local)
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [[77.5946, 12.9716], [77.6413, 12.9279]],
    "instructions": true,
    "geometry": true
  }'
```

#### Medium Distance (Inter-city)
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      [77.5946, 12.9716],  # Bangalore
      [78.4867, 17.3850]   # Hyderabad
    ],
    "instructions": true,
    "geometry": true,
    "preference": "fastest"
  }'
```

#### Long Distance (Cross-country)
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      [77.2090, 28.6139],  # Delhi
      [72.8777, 19.0760]   # Mumbai
    ],
    "instructions": true,
    "geometry": true,
    "preference": "fastest",
    "units": "km"
  }'
```

#### Maximum Distance Test (5000km)
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      [68.3685, 25.3960],  # Karachi area (if data available)
      [88.3639, 22.5726]   # Kolkata
    ],
    "instructions": false,
    "geometry": false
  }'
```

### Advanced Routing Options

#### Avoid Highways
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [[77.5946, 12.9716], [78.4867, 17.3850]],
    "options": {
      "avoid_features": ["highways"]
    }
  }'
```

#### Alternative Routes
```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [[77.5946, 12.9716], [78.4867, 17.3850]],
    "alternative_routes": {
      "target_count": 2,
      "weight_factor": 1.4
    }
  }'
```

### Response Format Examples

#### Successful Response
```json
{
  "bbox": [77.5946, 12.9716, 78.4867, 17.3850],
  "routes": [{
    "summary": {
      "distance": 574832.1,
      "duration": 20539.8
    },
    "segments": [{
      "distance": 574832.1,
      "duration": 20539.8,
      "steps": [...]
    }],
    "bbox": [77.5946, 12.9716, 78.4867, 17.3850],
    "geometry": "encoded_polyline_string",
    "way_points": [0, 142]
  }],
  "metadata": {
    "attribution": "openrouteservice.org, OpenStreetMap contributors",
    "service": "routing",
    "timestamp": *************,
    "query": {
      "coordinates": [[77.5946, 12.9716], [78.4867, 17.3850]],
      "profile": "driving-car",
      "format": "json"
    },
    "engine": {
      "version": "9.1.2",
      "build_date": "2025-04-10T21:25:30Z",
      "graph_date": "2025-07-13T18:41:18Z"
    }
  }
}
```

#### Error Response (Distance Exceeded)
```json
{
  "error": {
    "code": 2004,
    "message": "Request parameters exceed the server configuration limits. The approximated route distance must not be greater than 5000000.0 meters."
  },
  "info": {
    "engine": {
      "build_date": "2025-04-10T21:25:30Z",
      "version": "9.1.2"
    },
    "timestamp": *************
  }
}
```

## 📈 Performance Optimization

### Memory Optimization

#### For Systems with Limited RAM (8-16GB)
```yaml
# docker-compose.yml
environment:
  JAVA_HEAP_MIN: "6g"
  JAVA_HEAP_MAX: "10g"
deploy:
  resources:
    limits:
      memory: 14g
    reservations:
      memory: 8g
```

#### For High-Performance Systems (32GB+)
```yaml
# docker-compose.yml
environment:
  JAVA_HEAP_MIN: "24g"
  JAVA_HEAP_MAX: "30g"
deploy:
  resources:
    limits:
      memory: 36g
    reservations:
      memory: 24g
```

### Storage Optimization

```bash
# Use SSD for better I/O performance
# Mount data directory on fastest storage
# Regular cleanup of old containers
docker system prune -a

# Monitor disk usage
docker system df
```

### Network Optimization

```bash
# For production, consider using nginx reverse proxy
# Enable compression for API responses
# Use CDN for static assets
```

## 🔄 Maintenance and Updates

### Regular Maintenance

```bash
# Update OSM data (monthly recommended)
cd ors-docker/files
wget -O india-latest.osm.pbf.new https://download.geofabrik.de/asia/india-latest.osm.pbf
mv india-latest.osm.pbf.new india-latest.osm.pbf

# Rebuild graphs with new data
docker-compose down -v
docker-compose up -d

# Clean up old Docker images
docker image prune -a

# Monitor logs for errors
docker-compose logs --tail=100 ors-app
```

### Backup and Restore

```bash
# Backup built graphs (saves rebuild time)
docker run --rm -v openrouteservice_ors-graphs:/data -v $(pwd):/backup alpine tar czf /backup/ors-graphs-backup.tar.gz -C /data .

# Restore graphs
docker run --rm -v openrouteservice_ors-graphs:/data -v $(pwd):/backup alpine tar xzf /backup/ors-graphs-backup.tar.gz -C /data
```

## 🆘 Support and Resources

### Getting Help

1. **Check Logs First:**
   ```bash
   docker-compose logs ors-app
   ```

2. **Verify Configuration:**
   ```bash
   # Check if config is properly mounted
   docker exec ors-app cat /home/<USER>/config/ors-config.yml
   ```

3. **Test System Resources:**
   ```bash
   # Check available memory
   free -h

   # Check disk space
   df -h

   # Check Docker resources
   docker system df
   ```

### Useful Resources

- **OpenRouteService Documentation**: https://giscience.github.io/openrouteservice/
- **API Reference**: https://giscience.github.io/openrouteservice/api-reference/
- **Docker Hub**: https://hub.docker.com/r/heigit/openrouteservice
- **OSM Data Downloads**: https://download.geofabrik.de/

### Common Commands Reference

```bash
# Service Management
docker-compose up -d              # Start service
docker-compose down               # Stop service
docker-compose down -v            # Stop and remove data
docker-compose restart ors-app    # Restart service
docker-compose ps                 # Check status

# Monitoring
docker-compose logs -f ors-app    # Follow logs
docker stats ors-app              # Resource usage
curl http://localhost:8080/ors/v2/health  # Health check

# Maintenance
docker system prune -a            # Clean up Docker
docker-compose pull               # Update images
```

---

## 📝 Final Notes

**Current Status**: ✅ Service Running | **Port**: 8080 | **Max Distance**: 5000km

This guide provides everything needed to run OpenRouteService locally. The service is currently configured with:

- **Driving-car profile** enabled with 5000km maximum distance
- **Memory optimized** for systems with 12-32GB RAM
- **India OSM data** support (when properly configured)
- **Docker-based** deployment for easy management

For production use, consider enabling additional profiles, increasing memory allocation, and implementing proper monitoring and backup strategies.

**Remember**: The first startup takes 30-60 minutes to build routing graphs. Be patient and monitor the logs to track progress.
