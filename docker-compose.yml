services:
  ors-app:
    image: heigit/openrouteservice:latest
    container_name: ors-app
    ports:
      - "8080:8082"
    volumes:
      - ./ors-docker:/home/<USER>
      - ./custom-ors-config.yml:/home/<USER>/config/ors-config.yml
    environment:
      BUILD_GRAPHS: "True"
      REBUILD_GRAPHS: "True"
      JAVA_HEAP_MIN: "16g"
      JAVA_HEAP_MAX: "24g"
      OSM_FILE: "/home/<USER>/files/india-latest.osm.pbf"
      DATAREADER: "OSM"
      PROFILE_LANDMARKS: "false"
      ORS_CONFIG: "/home/<USER>/config/ors-config.yml"
    deploy:
      resources:
        limits:
          memory: 32g
        reservations:
          memory: 16g
